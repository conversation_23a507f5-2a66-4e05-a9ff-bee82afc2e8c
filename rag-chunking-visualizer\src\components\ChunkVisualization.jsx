import React, { useState, useMemo } from 'react';
import { Search, Eye, EyeOff, Co<PERSON>, Check, Filter, BarChart3 } from 'lucide-react';
import clsx from 'clsx';

const ChunkVisualization = ({ chunks = [], originalText = '', strategy = '' }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChunk, setSelectedChunk] = useState(null);
  const [showMetadata, setShowMetadata] = useState(true);
  const [copiedChunkId, setCopiedChunkId] = useState(null);
  const [filterBy, setFilterBy] = useState('all'); // 'all', 'small', 'medium', 'large'

  // Filter and search chunks
  const filteredChunks = useMemo(() => {
    let filtered = chunks;

    // Apply size filter
    if (filterBy !== 'all') {
      const avgSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length;
      filtered = chunks.filter(chunk => {
        switch (filterBy) {
          case 'small':
            return chunk.length < avgSize * 0.7;
          case 'medium':
            return chunk.length >= avgSize * 0.7 && chunk.length <= avgSize * 1.3;
          case 'large':
            return chunk.length > avgSize * 1.3;
          default:
            return true;
        }
      });
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(chunk =>
        chunk.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        chunk.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [chunks, searchTerm, filterBy]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (chunks.length === 0) return null;

    const sizes = chunks.map(chunk => chunk.length);
    const wordCounts = chunks.map(chunk => chunk.wordCount);

    return {
      totalChunks: chunks.length,
      avgSize: Math.round(sizes.reduce((sum, size) => sum + size, 0) / sizes.length),
      minSize: Math.min(...sizes),
      maxSize: Math.max(...sizes),
      avgWords: Math.round(wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length),
      totalCharacters: sizes.reduce((sum, size) => sum + size, 0),
      totalWords: wordCounts.reduce((sum, count) => sum + count, 0),
    };
  }, [chunks]);

  const copyToClipboard = async (text, chunkId) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedChunkId(chunkId);
      setTimeout(() => setCopiedChunkId(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const getChunkSizeColor = (chunk) => {
    if (!stats) return 'bg-secondary-100';
    
    const ratio = chunk.length / stats.avgSize;
    if (ratio < 0.7) return 'bg-blue-100 border-blue-200';
    if (ratio > 1.3) return 'bg-orange-100 border-orange-200';
    return 'bg-green-100 border-green-200';
  };

  const getChunkSizeLabel = (chunk) => {
    if (!stats) return 'Medium';
    
    const ratio = chunk.length / stats.avgSize;
    if (ratio < 0.7) return 'Small';
    if (ratio > 1.3) return 'Large';
    return 'Medium';
  };

  if (chunks.length === 0) {
    return (
      <div className="bg-white rounded-xl border border-secondary-200 p-8 text-center">
        <BarChart3 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-secondary-900 mb-2">No Chunks to Display</h3>
        <p className="text-secondary-600">Upload a PDF and select a chunking strategy to see the results.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Panel */}
      {stats && (
        <div className="bg-white rounded-xl border border-secondary-200 p-6">
          <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-primary-600" />
            Chunking Statistics
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-primary-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-600">{stats.totalChunks}</div>
              <div className="text-sm text-secondary-600">Total Chunks</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.avgSize}</div>
              <div className="text-sm text-secondary-600">Avg Size (chars)</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.avgWords}</div>
              <div className="text-sm text-secondary-600">Avg Words</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round((stats.totalCharacters / originalText.length) * 100)}%
              </div>
              <div className="text-sm text-secondary-600">Coverage</div>
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="bg-white rounded-xl border border-secondary-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search chunks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              />
            </div>
            
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            >
              <option value="all">All Sizes</option>
              <option value="small">Small Chunks</option>
              <option value="medium">Medium Chunks</option>
              <option value="large">Large Chunks</option>
            </select>
          </div>

          <button
            onClick={() => setShowMetadata(!showMetadata)}
            className="flex items-center space-x-2 px-4 py-2 text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50 rounded-lg transition-colors"
          >
            {showMetadata ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{showMetadata ? 'Hide' : 'Show'} Metadata</span>
          </button>
        </div>

        <div className="mt-4 text-sm text-secondary-600">
          Showing {filteredChunks.length} of {chunks.length} chunks
          {searchTerm && ` matching "${searchTerm}"`}
        </div>
      </div>

      {/* Chunks Display */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {filteredChunks.map((chunk, index) => (
          <div
            key={chunk.id}
            className={clsx(
              'border rounded-xl p-4 transition-all duration-200 hover:shadow-md cursor-pointer',
              getChunkSizeColor(chunk),
              {
                'ring-2 ring-primary-500 shadow-lg': selectedChunk?.id === chunk.id,
              }
            )}
            onClick={() => setSelectedChunk(selectedChunk?.id === chunk.id ? null : chunk)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-secondary-900">
                  Chunk {chunk.index + 1}
                </span>
                <span className={clsx(
                  'px-2 py-1 text-xs font-medium rounded-full',
                  {
                    'bg-blue-100 text-blue-800': getChunkSizeLabel(chunk) === 'Small',
                    'bg-green-100 text-green-800': getChunkSizeLabel(chunk) === 'Medium',
                    'bg-orange-100 text-orange-800': getChunkSizeLabel(chunk) === 'Large',
                  }
                )}>
                  {getChunkSizeLabel(chunk)}
                </span>
              </div>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  copyToClipboard(chunk.content, chunk.id);
                }}
                className="p-1 text-secondary-400 hover:text-secondary-600 hover:bg-white rounded transition-colors"
                title="Copy chunk content"
              >
                {copiedChunkId === chunk.id ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>

            <div className="text-sm text-secondary-700 leading-relaxed mb-3">
              {chunk.content.length > 200 && selectedChunk?.id !== chunk.id
                ? `${chunk.content.substring(0, 200)}...`
                : chunk.content
              }
            </div>

            {showMetadata && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-secondary-600 bg-white/50 rounded-lg p-2">
                <div>
                  <span className="font-medium">Length:</span> {chunk.length} chars
                </div>
                <div>
                  <span className="font-medium">Words:</span> {chunk.wordCount}
                </div>
                <div>
                  <span className="font-medium">Position:</span> {chunk.startPosition}-{chunk.endPosition}
                </div>
                <div>
                  <span className="font-medium">Strategy:</span> {chunk.strategy || strategy}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredChunks.length === 0 && searchTerm && (
        <div className="bg-white rounded-xl border border-secondary-200 p-8 text-center">
          <Search className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-secondary-900 mb-2">No Results Found</h3>
          <p className="text-secondary-600">
            No chunks match your search term "{searchTerm}". Try a different search or clear the filter.
          </p>
        </div>
      )}
    </div>
  );
};

export default ChunkVisualization;
