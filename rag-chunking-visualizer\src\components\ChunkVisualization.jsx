import React, { useState, useMemo } from 'react';
import { Search, Eye, EyeOff, Co<PERSON>, Check, Filter, BarChart3 } from 'lucide-react';
import clsx from 'clsx';

const ChunkVisualization = ({ chunks = [], originalText = '', strategy = '' }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChunk, setSelectedChunk] = useState(null);
  const [showMetadata, setShowMetadata] = useState(true);
  const [copiedChunkId, setCopiedChunkId] = useState(null);
  const [filterBy, setFilterBy] = useState('all'); // 'all', 'small', 'medium', 'large'

  // Filter and search chunks
  const filteredChunks = useMemo(() => {
    let filtered = chunks;

    // Apply size filter
    if (filterBy !== 'all') {
      const avgSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length;
      filtered = chunks.filter(chunk => {
        switch (filterBy) {
          case 'small':
            return chunk.length < avgSize * 0.7;
          case 'medium':
            return chunk.length >= avgSize * 0.7 && chunk.length <= avgSize * 1.3;
          case 'large':
            return chunk.length > avgSize * 1.3;
          default:
            return true;
        }
      });
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(chunk =>
        chunk.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        chunk.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [chunks, searchTerm, filterBy]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (chunks.length === 0) return null;

    const sizes = chunks.map(chunk => chunk.length);
    const wordCounts = chunks.map(chunk => chunk.wordCount);

    return {
      totalChunks: chunks.length,
      avgSize: Math.round(sizes.reduce((sum, size) => sum + size, 0) / sizes.length),
      minSize: Math.min(...sizes),
      maxSize: Math.max(...sizes),
      avgWords: Math.round(wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length),
      totalCharacters: sizes.reduce((sum, size) => sum + size, 0),
      totalWords: wordCounts.reduce((sum, count) => sum + count, 0),
    };
  }, [chunks]);

  const copyToClipboard = async (text, chunkId) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedChunkId(chunkId);
      setTimeout(() => setCopiedChunkId(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const getChunkSizeColor = (chunk) => {
    if (!stats) return 'bg-secondary-100';
    
    const ratio = chunk.length / stats.avgSize;
    if (ratio < 0.7) return 'bg-blue-100 border-blue-200';
    if (ratio > 1.3) return 'bg-orange-100 border-orange-200';
    return 'bg-green-100 border-green-200';
  };

  const getChunkSizeLabel = (chunk) => {
    if (!stats) return 'Medium';
    
    const ratio = chunk.length / stats.avgSize;
    if (ratio < 0.7) return 'Small';
    if (ratio > 1.3) return 'Large';
    return 'Medium';
  };

  if (chunks.length === 0) {
    return (
      <div className="text-center py-8 bg-gradient-to-br from-secondary-50 to-primary-50 rounded-xl border border-secondary-200">
        <div className="max-w-sm mx-auto">
          <div className="w-12 h-12 mx-auto mb-3 bg-secondary-200 rounded-full flex items-center justify-center">
            <BarChart3 className="w-6 h-6 text-secondary-400" />
          </div>
          <h3 className="text-base font-semibold text-secondary-900 mb-1">
            No Chunks to Display
          </h3>
          <p className="text-sm text-secondary-600">
            {strategy ? 'Click "Execute Chunking" to process your document.' : 'Select a chunking strategy to get started.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Compact Stats and Controls */}
      {stats && (
        <div className="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-4 border border-primary-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
            {/* Statistics */}
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="text-lg font-bold text-primary-700">{stats.totalChunks}</div>
                <div className="text-xs text-secondary-600">Chunks</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-primary-700">{stats.avgSize}</div>
                <div className="text-xs text-secondary-600">Avg Size</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-primary-700">{stats.avgWords}</div>
                <div className="text-xs text-secondary-600">Avg Words</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-primary-700">
                  {Math.round((stats.totalCharacters / originalText.length) * 100)}%
                </div>
                <div className="text-xs text-secondary-600">Coverage</div>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-secondary-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 pr-3 py-1.5 border border-secondary-300 rounded-lg focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm w-32"
                />
              </div>

              {/* Filter */}
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value)}
                className="px-2 py-1.5 border border-secondary-300 rounded-lg focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
              >
                <option value="all">All</option>
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </select>

              {/* Toggle Metadata */}
              <button
                onClick={() => setShowMetadata(!showMetadata)}
                className={clsx(
                  'flex items-center space-x-1 px-2 py-1.5 rounded-lg text-xs font-medium transition-all duration-200',
                  showMetadata
                    ? 'bg-primary-200 text-primary-800 hover:bg-primary-300'
                    : 'bg-secondary-200 text-secondary-700 hover:bg-secondary-300'
                )}
              >
                {showMetadata ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                <span>{showMetadata ? 'Hide' : 'Show'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="text-sm text-secondary-600 px-2">
        Showing {filteredChunks.length} of {chunks.length} chunks
        {searchTerm && ` matching "${searchTerm}"`}
      </div>

      {/* Chunks Display */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {filteredChunks.map((chunk, index) => (
          <div
            key={chunk.id}
            className={clsx(
              'border rounded-xl p-4 transition-all duration-200 hover:shadow-md cursor-pointer',
              getChunkSizeColor(chunk),
              {
                'ring-2 ring-primary-500 shadow-lg': selectedChunk?.id === chunk.id,
              }
            )}
            onClick={() => setSelectedChunk(selectedChunk?.id === chunk.id ? null : chunk)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-secondary-900">
                  Chunk {chunk.index + 1}
                </span>
                <span className={clsx(
                  'px-2 py-1 text-xs font-medium rounded-full',
                  {
                    'bg-blue-100 text-blue-800': getChunkSizeLabel(chunk) === 'Small',
                    'bg-green-100 text-green-800': getChunkSizeLabel(chunk) === 'Medium',
                    'bg-orange-100 text-orange-800': getChunkSizeLabel(chunk) === 'Large',
                  }
                )}>
                  {getChunkSizeLabel(chunk)}
                </span>
              </div>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  copyToClipboard(chunk.content, chunk.id);
                }}
                className="p-1 text-secondary-400 hover:text-secondary-600 hover:bg-white rounded transition-colors"
                title="Copy chunk content"
              >
                {copiedChunkId === chunk.id ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>

            <div className="text-sm text-secondary-700 leading-relaxed mb-3">
              {chunk.content.length > 200 && selectedChunk?.id !== chunk.id
                ? `${chunk.content.substring(0, 200)}...`
                : chunk.content
              }
            </div>

            {showMetadata && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-secondary-600 bg-white/50 rounded-lg p-2">
                <div>
                  <span className="font-medium">Length:</span> {chunk.length} chars
                </div>
                <div>
                  <span className="font-medium">Words:</span> {chunk.wordCount}
                </div>
                <div>
                  <span className="font-medium">Position:</span> {chunk.startPosition}-{chunk.endPosition}
                </div>
                <div>
                  <span className="font-medium">Strategy:</span> {chunk.strategy || strategy}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredChunks.length === 0 && searchTerm && (
        <div className="bg-white rounded-xl border border-secondary-200 p-8 text-center">
          <Search className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-secondary-900 mb-2">No Results Found</h3>
          <p className="text-secondary-600">
            No chunks match your search term "{searchTerm}". Try a different search or clear the filter.
          </p>
        </div>
      )}
    </div>
  );
};

export default ChunkVisualization;
