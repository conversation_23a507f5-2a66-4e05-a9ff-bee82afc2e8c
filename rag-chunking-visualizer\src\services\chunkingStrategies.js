/**
 * Chunking Strategies for RAG Systems
 * Implements various text chunking approaches with configurable parameters
 */

/**
 * Base Chunking Strategy Class
 */
class BaseChunkingStrategy {
  constructor(name, description) {
    this.name = name;
    this.description = description;
  }

  /**
   * Abstract method to be implemented by subclasses
   * @param {string} text - Text to chunk
   * @param {Object} options - Chunking options
   * @returns {Array<Object>} Array of chunks with metadata
   */
  chunk(text, options = {}) {
    throw new Error('chunk method must be implemented by subclass');
  }

  /**
   * Create chunk object with metadata
   * @param {string} content - Chunk content
   * @param {number} index - Chunk index
   * @param {number} startPos - Start position in original text
   * @param {number} endPos - End position in original text
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Chunk object
   */
  createChunk(content, index, startPos, endPos, metadata = {}) {
    return {
      id: `chunk_${index}`,
      content: content.trim(),
      index,
      startPosition: startPos,
      endPosition: endPos,
      length: content.length,
      wordCount: this.countWords(content),
      ...metadata
    };
  }

  /**
   * Count words in text
   * @param {string} text - Text to count
   * @returns {number} Word count
   */
  countWords(text) {
    if (!text) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }
}

/**
 * Fixed-Size Chunking Strategy
 * Splits text into chunks of fixed character or token count
 */
class FixedSizeChunking extends BaseChunkingStrategy {
  constructor() {
    super(
      'Fixed Size',
      'Splits text into chunks of fixed character or word count with optional overlap'
    );
  }

  chunk(text, options = {}) {
    const {
      chunkSize = 1000,
      overlap = 200,
      unit = 'characters' // 'characters' or 'words'
    } = options;

    if (!text || text.length === 0) return [];

    const chunks = [];
    let currentPosition = 0;
    let chunkIndex = 0;

    if (unit === 'words') {
      return this.chunkByWords(text, chunkSize, overlap);
    } else {
      return this.chunkByCharacters(text, chunkSize, overlap);
    }
  }

  chunkByCharacters(text, chunkSize, overlap) {
    const chunks = [];
    let currentPosition = 0;
    let chunkIndex = 0;

    while (currentPosition < text.length) {
      const endPosition = Math.min(currentPosition + chunkSize, text.length);
      let chunkContent = text.slice(currentPosition, endPosition);

      // Try to break at word boundaries if possible
      if (endPosition < text.length) {
        const lastSpaceIndex = chunkContent.lastIndexOf(' ');
        if (lastSpaceIndex > chunkSize * 0.8) { // Only if we don't lose too much content
          chunkContent = chunkContent.slice(0, lastSpaceIndex);
        }
      }

      chunks.push(this.createChunk(
        chunkContent,
        chunkIndex,
        currentPosition,
        currentPosition + chunkContent.length,
        {
          strategy: 'fixed-size',
          unit: 'characters',
          chunkSize,
          overlap,
          hasOverlap: chunkIndex > 0 && overlap > 0
        }
      ));

      currentPosition += chunkContent.length - overlap;
      chunkIndex++;

      // Prevent infinite loop
      if (currentPosition <= 0) break;
    }

    return chunks;
  }

  chunkByWords(text, chunkSize, overlap) {
    const words = text.split(/\s+/);
    const chunks = [];
    let currentPosition = 0;
    let chunkIndex = 0;

    while (currentPosition < words.length) {
      const endPosition = Math.min(currentPosition + chunkSize, words.length);
      const chunkWords = words.slice(currentPosition, endPosition);
      const chunkContent = chunkWords.join(' ');

      // Calculate character positions
      const startCharPos = words.slice(0, currentPosition).join(' ').length;
      const endCharPos = startCharPos + chunkContent.length;

      chunks.push(this.createChunk(
        chunkContent,
        chunkIndex,
        startCharPos,
        endCharPos,
        {
          strategy: 'fixed-size',
          unit: 'words',
          chunkSize,
          overlap,
          hasOverlap: chunkIndex > 0 && overlap > 0
        }
      ));

      currentPosition += chunkSize - overlap;
      chunkIndex++;

      // Prevent infinite loop
      if (currentPosition <= 0) break;
    }

    return chunks;
  }

  getDefaultOptions() {
    return {
      chunkSize: 1000,
      overlap: 200,
      unit: 'characters'
    };
  }

  getConfigSchema() {
    return {
      chunkSize: {
        type: 'number',
        label: 'Chunk Size',
        description: 'Size of each chunk in characters or words',
        min: 100,
        max: 5000,
        default: 1000
      },
      overlap: {
        type: 'number',
        label: 'Overlap',
        description: 'Number of characters/words to overlap between chunks',
        min: 0,
        max: 1000,
        default: 200
      },
      unit: {
        type: 'select',
        label: 'Unit',
        description: 'Unit for measuring chunk size',
        options: [
          { value: 'characters', label: 'Characters' },
          { value: 'words', label: 'Words' }
        ],
        default: 'characters'
      }
    };
  }
}

/**
 * Sentence-Based Chunking Strategy
 * Groups sentences into chunks based on sentence boundaries
 */
class SentenceBasedChunking extends BaseChunkingStrategy {
  constructor() {
    super(
      'Sentence Based',
      'Groups sentences into chunks while respecting sentence boundaries'
    );
  }

  chunk(text, options = {}) {
    const {
      maxChunkSize = 1000,
      sentencesPerChunk = 5,
      mode = 'sentence_count' // 'sentence_count' or 'max_size'
    } = options;

    if (!text || text.length === 0) return [];

    const sentences = this.splitIntoSentences(text);
    const chunks = [];
    let currentChunk = [];
    let currentSize = 0;
    let chunkIndex = 0;
    let charPosition = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      const sentenceLength = sentence.length;

      if (mode === 'sentence_count') {
        currentChunk.push(sentence);
        currentSize += sentenceLength;

        if (currentChunk.length >= sentencesPerChunk || i === sentences.length - 1) {
          const chunkContent = currentChunk.join(' ');
          chunks.push(this.createChunk(
            chunkContent,
            chunkIndex,
            charPosition - currentSize,
            charPosition,
            {
              strategy: 'sentence-based',
              mode,
              sentenceCount: currentChunk.length,
              maxChunkSize,
              sentencesPerChunk
            }
          ));

          currentChunk = [];
          currentSize = 0;
          chunkIndex++;
        }
      } else { // max_size mode
        if (currentSize + sentenceLength > maxChunkSize && currentChunk.length > 0) {
          const chunkContent = currentChunk.join(' ');
          chunks.push(this.createChunk(
            chunkContent,
            chunkIndex,
            charPosition - currentSize,
            charPosition,
            {
              strategy: 'sentence-based',
              mode,
              sentenceCount: currentChunk.length,
              maxChunkSize,
              sentencesPerChunk
            }
          ));

          currentChunk = [sentence];
          currentSize = sentenceLength;
          chunkIndex++;
        } else {
          currentChunk.push(sentence);
          currentSize += sentenceLength;
        }
      }

      charPosition += sentenceLength + 1; // +1 for space
    }

    // Add remaining sentences if any
    if (currentChunk.length > 0) {
      const chunkContent = currentChunk.join(' ');
      chunks.push(this.createChunk(
        chunkContent,
        chunkIndex,
        charPosition - currentSize,
        charPosition,
        {
          strategy: 'sentence-based',
          mode,
          sentenceCount: currentChunk.length,
          maxChunkSize,
          sentencesPerChunk
        }
      ));
    }

    return chunks;
  }

  splitIntoSentences(text) {
    // Enhanced sentence splitting with better handling of abbreviations
    return text
      .replace(/([.!?])\s+/g, '$1|SPLIT|')
      .split('|SPLIT|')
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0);
  }

  getDefaultOptions() {
    return {
      maxChunkSize: 1000,
      sentencesPerChunk: 5,
      mode: 'sentence_count'
    };
  }

  getConfigSchema() {
    return {
      mode: {
        type: 'select',
        label: 'Chunking Mode',
        description: 'How to group sentences into chunks',
        options: [
          { value: 'sentence_count', label: 'Fixed Sentence Count' },
          { value: 'max_size', label: 'Maximum Size' }
        ],
        default: 'sentence_count'
      },
      sentencesPerChunk: {
        type: 'number',
        label: 'Sentences per Chunk',
        description: 'Number of sentences per chunk (sentence_count mode)',
        min: 1,
        max: 20,
        default: 5
      },
      maxChunkSize: {
        type: 'number',
        label: 'Max Chunk Size',
        description: 'Maximum characters per chunk (max_size mode)',
        min: 200,
        max: 3000,
        default: 1000
      }
    };
  }
}

/**
 * Paragraph-Based Chunking Strategy
 * Splits text based on paragraph boundaries
 */
class ParagraphBasedChunking extends BaseChunkingStrategy {
  constructor() {
    super(
      'Paragraph Based',
      'Splits text into chunks based on paragraph boundaries with size constraints'
    );
  }

  chunk(text, options = {}) {
    const {
      maxChunkSize = 1500,
      minChunkSize = 200,
      combineSmallParagraphs = true
    } = options;

    if (!text || text.length === 0) return [];

    const paragraphs = this.splitIntoParagraphs(text);
    const chunks = [];
    let currentChunk = [];
    let currentSize = 0;
    let chunkIndex = 0;
    let charPosition = 0;

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      const paragraphLength = paragraph.length;

      if (currentSize + paragraphLength > maxChunkSize && currentChunk.length > 0) {
        // Create chunk from current paragraphs
        const chunkContent = currentChunk.join('\n\n');
        chunks.push(this.createChunk(
          chunkContent,
          chunkIndex,
          charPosition - currentSize,
          charPosition,
          {
            strategy: 'paragraph-based',
            paragraphCount: currentChunk.length,
            maxChunkSize,
            minChunkSize
          }
        ));

        currentChunk = [paragraph];
        currentSize = paragraphLength;
        chunkIndex++;
      } else {
        currentChunk.push(paragraph);
        currentSize += paragraphLength + 2; // +2 for \n\n
      }

      charPosition += paragraphLength + 2;
    }

    // Add remaining paragraphs
    if (currentChunk.length > 0) {
      const chunkContent = currentChunk.join('\n\n');
      chunks.push(this.createChunk(
        chunkContent,
        chunkIndex,
        charPosition - currentSize,
        charPosition,
        {
          strategy: 'paragraph-based',
          paragraphCount: currentChunk.length,
          maxChunkSize,
          minChunkSize
        }
      ));
    }

    return chunks;
  }

  splitIntoParagraphs(text) {
    return text
      .split(/\n\s*\n/)
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0);
  }

  getDefaultOptions() {
    return {
      maxChunkSize: 1500,
      minChunkSize: 200,
      combineSmallParagraphs: true
    };
  }

  getConfigSchema() {
    return {
      maxChunkSize: {
        type: 'number',
        label: 'Max Chunk Size',
        description: 'Maximum characters per chunk',
        min: 500,
        max: 5000,
        default: 1500
      },
      minChunkSize: {
        type: 'number',
        label: 'Min Chunk Size',
        description: 'Minimum characters per chunk',
        min: 100,
        max: 1000,
        default: 200
      },
      combineSmallParagraphs: {
        type: 'boolean',
        label: 'Combine Small Paragraphs',
        description: 'Combine small paragraphs to meet minimum size',
        default: true
      }
    };
  }
}

/**
 * Recursive Chunking Strategy
 * Hierarchically splits text using multiple delimiters
 */
class RecursiveChunking extends BaseChunkingStrategy {
  constructor() {
    super(
      'Recursive',
      'Hierarchically splits text using multiple delimiters (paragraphs, sentences, words)'
    );
  }

  chunk(text, options = {}) {
    const {
      chunkSize = 1000,
      overlap = 200,
      separators = ['\n\n', '\n', '. ', ' ']
    } = options;

    if (!text || text.length === 0) return [];

    return this.recursiveChunk(text, chunkSize, overlap, separators, 0);
  }

  recursiveChunk(text, chunkSize, overlap, separators, depth = 0, startPos = 0) {
    if (text.length <= chunkSize) {
      return [this.createChunk(
        text,
        0,
        startPos,
        startPos + text.length,
        {
          strategy: 'recursive',
          depth,
          separator: separators[Math.min(depth, separators.length - 1)],
          chunkSize,
          overlap
        }
      )];
    }

    if (depth >= separators.length) {
      // Fallback to character-based splitting
      return this.fallbackChunk(text, chunkSize, overlap, startPos);
    }

    const separator = separators[depth];
    const parts = text.split(separator);
    const chunks = [];
    let currentChunk = '';
    let currentPos = startPos;
    let chunkIndex = 0;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      const partWithSeparator = i < parts.length - 1 ? part + separator : part;

      if (currentChunk.length + partWithSeparator.length <= chunkSize) {
        currentChunk += partWithSeparator;
      } else {
        if (currentChunk) {
          // Process current chunk recursively if it's still too large
          if (currentChunk.length > chunkSize) {
            const subChunks = this.recursiveChunk(
              currentChunk,
              chunkSize,
              overlap,
              separators,
              depth + 1,
              currentPos
            );
            chunks.push(...subChunks);
          } else {
            chunks.push(this.createChunk(
              currentChunk,
              chunkIndex,
              currentPos,
              currentPos + currentChunk.length,
              {
                strategy: 'recursive',
                depth,
                separator,
                chunkSize,
                overlap
              }
            ));
          }
          currentPos += currentChunk.length;
          chunkIndex++;
        }

        currentChunk = partWithSeparator;
      }
    }

    // Handle remaining chunk
    if (currentChunk) {
      if (currentChunk.length > chunkSize) {
        const subChunks = this.recursiveChunk(
          currentChunk,
          chunkSize,
          overlap,
          separators,
          depth + 1,
          currentPos
        );
        chunks.push(...subChunks);
      } else {
        chunks.push(this.createChunk(
          currentChunk,
          chunkIndex,
          currentPos,
          currentPos + currentChunk.length,
          {
            strategy: 'recursive',
            depth,
            separator,
            chunkSize,
            overlap
          }
        ));
      }
    }

    return chunks;
  }

  fallbackChunk(text, chunkSize, overlap, startPos) {
    const chunks = [];
    let currentPosition = 0;
    let chunkIndex = 0;

    while (currentPosition < text.length) {
      const endPosition = Math.min(currentPosition + chunkSize, text.length);
      const chunkContent = text.slice(currentPosition, endPosition);

      chunks.push(this.createChunk(
        chunkContent,
        chunkIndex,
        startPos + currentPosition,
        startPos + endPosition,
        {
          strategy: 'recursive',
          depth: 'fallback',
          separator: 'character',
          chunkSize,
          overlap
        }
      ));

      currentPosition += chunkSize - overlap;
      chunkIndex++;
    }

    return chunks;
  }

  getDefaultOptions() {
    return {
      chunkSize: 1000,
      overlap: 200,
      separators: ['\n\n', '\n', '. ', ' ']
    };
  }

  getConfigSchema() {
    return {
      chunkSize: {
        type: 'number',
        label: 'Chunk Size',
        description: 'Target size for each chunk',
        min: 200,
        max: 3000,
        default: 1000
      },
      overlap: {
        type: 'number',
        label: 'Overlap',
        description: 'Overlap between chunks',
        min: 0,
        max: 500,
        default: 200
      }
    };
  }
}

// Export strategies
export const chunkingStrategies = {
  'fixed-size': new FixedSizeChunking(),
  'sentence-based': new SentenceBasedChunking(),
  'paragraph-based': new ParagraphBasedChunking(),
  'recursive': new RecursiveChunking(),
};

export default chunkingStrategies;
