# RAG Chunking Visualizer

A comprehensive web application for analyzing and visualizing different text chunking strategies for Retrieval-Augmented Generation (RAG) systems. This tool allows users to upload PDF documents, apply various chunking strategies, and visualize the results with detailed metadata and statistics.

## Features

### 🔄 Multiple Chunking Strategies
- **Fixed-Size Chunking**: Character or word-based chunking with configurable size and overlap
- **Sentence-Based Chunking**: Groups sentences with size constraints or fixed sentence counts
- **Paragraph-Based Chunking**: Respects paragraph boundaries with size optimization
- **Recursive Chunking**: Hierarchical splitting using multiple delimiters

### 📄 PDF Processing
- Drag-and-drop PDF upload with validation
- Text extraction using pdf-parse library
- Document metadata analysis (pages, words, characters, paragraphs)
- Large file handling support

### 📊 Interactive Visualization
- Scrollable, searchable chunk display
- Chunk size distribution and statistics
- Metadata visualization for each chunk
- Strategy comparison capabilities

### ⚙️ Configuration Options
- Customizable parameters for each chunking strategy
- Real-time configuration updates
- Strategy-specific optimization settings
- Advanced options for fine-tuning

## Technology Stack

- **Frontend**: React 18 with Vite
- **Styling**: Tailwind CSS with custom animations
- **PDF Processing**: pdf-parse library
- **UI Components**: Lucide React icons
- **File Handling**: react-dropzone

## Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn package manager

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

### Usage

1. **Upload a PDF**: Use the drag-and-drop interface or click to browse for a PDF file
2. **Try Demo Mode**: Click "Try Demo with Sample Text" to test with sample content
3. **Select Strategy**: Choose from available chunking strategies in the dropdown
4. **Configure Parameters**: Adjust strategy-specific settings as needed
5. **Apply Chunking**: Click "Apply Chunking Strategy" to process the text
6. **Analyze Results**: View chunks, statistics, and metadata in the visualization panel

## Chunking Strategies

### Fixed-Size Chunking
- **Parameters**: Chunk size, overlap, unit (characters/words)
- **Use Case**: Consistent chunk sizes for embedding models with fixed input limits

### Sentence-Based Chunking
- **Parameters**: Max chunk size, sentences per chunk, mode
- **Use Case**: Preserving sentence boundaries for better semantic coherence

### Paragraph-Based Chunking
- **Parameters**: Max/min chunk size, combine small paragraphs option
- **Use Case**: Maintaining document structure and topic coherence

### Recursive Chunking
- **Parameters**: Chunk size, overlap, separator hierarchy
- **Use Case**: Hierarchical text splitting with fallback mechanisms

## Project Structure

```
src/
├── components/
│   ├── PDFUpload.jsx          # File upload component
│   ├── StrategySelector.jsx   # Strategy selection and configuration
│   └── ChunkVisualization.jsx # Results display and visualization
├── services/
│   ├── pdfService.js          # PDF processing and text extraction
│   └── chunkingStrategies.js  # Chunking strategy implementations
├── utils/
│   └── sampleText.js          # Demo content for testing
├── App.jsx                    # Main application component
└── index.css                  # Global styles and Tailwind configuration
```

## Built for Question 3 - RAG Chunking Strategy Assignment

This application was built as a solution to Question 3 of the RAG systems assignment, demonstrating:
- Multiple chunking strategy implementations
- Interactive visualization of chunking results
- PDF processing and text extraction capabilities
- Configurable parameters for strategy optimization
- Comprehensive analysis and comparison tools
