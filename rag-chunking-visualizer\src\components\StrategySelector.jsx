import React, { useState, useEffect } from 'react';
import { Settings, Info, RotateCcw, Play } from 'lucide-react';
import clsx from 'clsx';
import chunkingStrategies from '../services/chunkingStrategies';

const StrategySelector = ({ 
  selectedStrategy, 
  onStrategyChange, 
  onConfigChange, 
  onExecute,
  isProcessing = false 
}) => {
  const [config, setConfig] = useState({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Initialize config when strategy changes
  useEffect(() => {
    if (selectedStrategy && chunkingStrategies[selectedStrategy]) {
      const defaultOptions = chunkingStrategies[selectedStrategy].getDefaultOptions();
      setConfig(defaultOptions);
      onConfigChange(defaultOptions);
    }
  }, [selectedStrategy, onConfigChange]);

  const handleConfigChange = (key, value) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    onConfigChange(newConfig);
  };

  const resetToDefaults = () => {
    if (selectedStrategy && chunkingStrategies[selectedStrategy]) {
      const defaultOptions = chunkingStrategies[selectedStrategy].getDefaultOptions();
      setConfig(defaultOptions);
      onConfigChange(defaultOptions);
    }
  };

  const renderConfigField = (key, fieldConfig) => {
    const value = config[key] ?? fieldConfig.default;

    switch (fieldConfig.type) {
      case 'number':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-secondary-700">
              {fieldConfig.label}
              {fieldConfig.description && (
                <div className="text-xs text-secondary-500 mt-1">
                  {fieldConfig.description}
                </div>
              )}
            </label>
            <input
              type="number"
              min={fieldConfig.min}
              max={fieldConfig.max}
              value={value}
              onChange={(e) => handleConfigChange(key, parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              disabled={isProcessing}
            />
            {fieldConfig.min !== undefined && fieldConfig.max !== undefined && (
              <div className="text-xs text-secondary-500">
                Range: {fieldConfig.min} - {fieldConfig.max}
              </div>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-secondary-700">
              {fieldConfig.label}
              {fieldConfig.description && (
                <div className="text-xs text-secondary-500 mt-1">
                  {fieldConfig.description}
                </div>
              )}
            </label>
            <select
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              disabled={isProcessing}
            >
              {fieldConfig.options.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      case 'boolean':
        return (
          <div key={key} className="flex items-center space-x-3">
            <input
              type="checkbox"
              id={key}
              checked={value}
              onChange={(e) => handleConfigChange(key, e.target.checked)}
              className="w-4 h-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500"
              disabled={isProcessing}
            />
            <label htmlFor={key} className="text-sm font-medium text-secondary-700">
              {fieldConfig.label}
              {fieldConfig.description && (
                <div className="text-xs text-secondary-500 mt-1">
                  {fieldConfig.description}
                </div>
              )}
            </label>
          </div>
        );

      default:
        return null;
    }
  };

  const strategyOptions = Object.entries(chunkingStrategies).map(([key, strategy]) => ({
    value: key,
    label: strategy.name,
    description: strategy.description
  }));

  const currentStrategy = selectedStrategy ? chunkingStrategies[selectedStrategy] : null;
  const configSchema = currentStrategy ? currentStrategy.getConfigSchema() : {};

  return (
    <div className="bg-white rounded-xl border border-secondary-200 p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-secondary-900 flex items-center">
          <Settings className="w-5 h-5 mr-2 text-primary-600" />
          Chunking Strategy
        </h3>
        
        {selectedStrategy && (
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 px-3 py-1.5 text-sm text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50 rounded-lg transition-colors"
            disabled={isProcessing}
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset</span>
          </button>
        )}
      </div>

      {/* Strategy Selection */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-secondary-700">
          Select Strategy
        </label>
        <select
          value={selectedStrategy || ''}
          onChange={(e) => onStrategyChange(e.target.value)}
          className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
          disabled={isProcessing}
        >
          <option value="">Choose a chunking strategy...</option>
          {strategyOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Strategy Description */}
      {currentStrategy && (
        <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-primary-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-primary-900 mb-1">
                {currentStrategy.name} Strategy
              </h4>
              <p className="text-sm text-primary-700">
                {currentStrategy.description}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Options */}
      {selectedStrategy && Object.keys(configSchema).length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-secondary-900">Configuration</h4>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-primary-600 hover:text-primary-700 transition-colors"
            >
              {showAdvanced ? 'Hide Advanced' : 'Show Advanced'}
            </button>
          </div>

          <div className="space-y-4">
            {Object.entries(configSchema).map(([key, fieldConfig]) => {
              // Show basic options by default, advanced options only when toggled
              const isAdvanced = ['overlap', 'separators', 'minChunkSize'].includes(key);
              
              if (isAdvanced && !showAdvanced) {
                return null;
              }

              return renderConfigField(key, fieldConfig);
            })}
          </div>
        </div>
      )}

      {/* Execute Button */}
      {selectedStrategy && (
        <div className="pt-4 border-t border-secondary-200">
          <button
            onClick={onExecute}
            disabled={isProcessing}
            className={clsx(
              'w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200',
              {
                'bg-primary-600 hover:bg-primary-700 text-white hover:shadow-lg hover:scale-[1.02]': !isProcessing,
                'bg-secondary-300 text-secondary-500 cursor-not-allowed': isProcessing,
              }
            )}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-secondary-500 border-t-transparent"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>Apply Chunking Strategy</span>
              </>
            )}
          </button>
        </div>
      )}

      {/* Current Configuration Summary */}
      {selectedStrategy && Object.keys(config).length > 0 && (
        <div className="bg-secondary-50 rounded-lg p-4">
          <h5 className="text-sm font-medium text-secondary-900 mb-2">Current Configuration:</h5>
          <div className="text-xs text-secondary-600 space-y-1">
            {Object.entries(config).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                <span className="font-medium">{String(value)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategySelector;
