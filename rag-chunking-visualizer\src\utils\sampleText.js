// Sample text for testing chunking strategies when no PDF is available
export const sampleText = `
Artificial Intelligence and Machine Learning: A Comprehensive Overview

Introduction

Artificial Intelligence (AI) and Machine Learning (ML) have become transformative technologies that are reshaping industries, revolutionizing how we work, and changing the way we interact with technology. This comprehensive overview explores the fundamental concepts, applications, and future implications of these rapidly evolving fields.

What is Artificial Intelligence?

Artificial Intelligence refers to the simulation of human intelligence in machines that are programmed to think and learn like humans. The term may also be applied to any machine that exhibits traits associated with a human mind such as learning and problem-solving. AI systems can perform tasks that typically require human intelligence, including visual perception, speech recognition, decision-making, and language translation.

The field of AI encompasses several subdomains, including machine learning, natural language processing, computer vision, robotics, and expert systems. Each of these areas focuses on different aspects of creating intelligent systems that can operate autonomously or assist humans in complex tasks.

Machine Learning Fundamentals

Machine Learning is a subset of AI that focuses on the development of algorithms and statistical models that enable computer systems to improve their performance on a specific task through experience, without being explicitly programmed for every scenario. ML algorithms build mathematical models based on training data to make predictions or decisions without being explicitly programmed to perform the task.

There are three main types of machine learning: supervised learning, unsupervised learning, and reinforcement learning. Supervised learning uses labeled training data to learn a mapping from inputs to outputs. Unsupervised learning finds hidden patterns in data without labeled examples. Reinforcement learning involves an agent learning to make decisions by taking actions in an environment to maximize cumulative reward.

Deep Learning and Neural Networks

Deep Learning is a specialized subset of machine learning that uses artificial neural networks with multiple layers (hence "deep") to model and understand complex patterns in data. These neural networks are inspired by the structure and function of the human brain, consisting of interconnected nodes (neurons) that process and transmit information.

Deep learning has achieved remarkable success in various applications, including image recognition, natural language processing, speech recognition, and game playing. The ability of deep neural networks to automatically learn hierarchical representations of data has made them particularly effective for tasks involving large amounts of unstructured data.

Applications Across Industries

The applications of AI and ML span virtually every industry and sector. In healthcare, AI systems assist in medical diagnosis, drug discovery, and personalized treatment plans. In finance, machine learning algorithms detect fraud, assess credit risk, and enable algorithmic trading. The automotive industry leverages AI for autonomous vehicles and advanced driver assistance systems.

In technology and software, AI powers recommendation systems, search engines, virtual assistants, and content moderation. Manufacturing industries use AI for predictive maintenance, quality control, and supply chain optimization. Entertainment and media companies employ AI for content creation, personalization, and audience analysis.

Challenges and Ethical Considerations

Despite the tremendous potential of AI and ML, these technologies also present significant challenges and ethical considerations. Issues such as algorithmic bias, privacy concerns, job displacement, and the need for transparency in AI decision-making processes require careful attention and thoughtful solutions.

The development of AI systems must consider fairness, accountability, and transparency to ensure that these technologies benefit society as a whole. This includes addressing biases in training data, ensuring privacy protection, and maintaining human oversight in critical decision-making processes.

Future Directions and Implications

The future of AI and ML holds immense promise, with ongoing research focusing on areas such as artificial general intelligence, quantum machine learning, and neuromorphic computing. As these technologies continue to evolve, they will likely become even more integrated into our daily lives and business operations.

The continued advancement of AI and ML will require collaboration between technologists, policymakers, ethicists, and society at large to ensure that these powerful tools are developed and deployed responsibly. Education and workforce development will also be crucial to prepare individuals for an AI-driven future.

Conclusion

Artificial Intelligence and Machine Learning represent some of the most significant technological advances of our time. Their potential to solve complex problems, improve efficiency, and enhance human capabilities is enormous. However, realizing this potential while addressing the associated challenges will require continued innovation, thoughtful regulation, and a commitment to ethical development practices.

As we move forward, the successful integration of AI and ML into society will depend on our ability to harness their power while maintaining human values and ensuring that the benefits are distributed equitably across all segments of society.
`;

export const sampleMetadata = {
  pages: 1,
  wordCount: 650,
  characterCount: sampleText.length,
  paragraphCount: 12,
  fileName: 'sample-ai-ml-overview.txt',
  fileSize: sampleText.length,
  extractedAt: new Date().toISOString()
};
