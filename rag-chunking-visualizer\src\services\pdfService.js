import pdfParse from 'pdf-parse';

/**
 * PDF Processing Service
 * Handles PDF text extraction and preprocessing
 */
class PDFService {
  /**
   * Extract text content from PDF file
   * @param {File} file - PDF file to process
   * @returns {Promise<Object>} Extracted text and metadata
   */
  async extractText(file) {
    try {
      // Validate file type
      if (file.type !== 'application/pdf') {
        throw new Error('Invalid file type. Please upload a PDF file.');
      }

      // Convert file to buffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Extract text using pdf-parse
      const data = await pdfParse(buffer, {
        // Options for better text extraction
        normalizeWhitespace: true,
        disableCombineTextItems: false,
      });

      // Process and clean the extracted text
      const cleanedText = this.cleanText(data.text);

      return {
        success: true,
        data: {
          text: cleanedText,
          originalText: data.text,
          metadata: {
            pages: data.numpages,
            info: data.info,
            version: data.version,
            wordCount: this.countWords(cleanedText),
            characterCount: cleanedText.length,
            paragraphCount: this.countParagraphs(cleanedText),
            extractedAt: new Date().toISOString(),
            fileName: file.name,
            fileSize: file.size,
          }
        }
      };
    } catch (error) {
      console.error('PDF extraction error:', error);
      return {
        success: false,
        error: error.message || 'Failed to extract text from PDF'
      };
    }
  }

  /**
   * Clean and normalize extracted text
   * @param {string} text - Raw extracted text
   * @returns {string} Cleaned text
   */
  cleanText(text) {
    if (!text) return '';

    return text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove page breaks and form feeds
      .replace(/[\f\r]/g, '')
      // Normalize line breaks
      .replace(/\n\s*\n/g, '\n\n')
      // Remove leading/trailing whitespace
      .trim();
  }

  /**
   * Count words in text
   * @param {string} text - Text to count
   * @returns {number} Word count
   */
  countWords(text) {
    if (!text) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Count paragraphs in text
   * @param {string} text - Text to count
   * @returns {number} Paragraph count
   */
  countParagraphs(text) {
    if (!text) return 0;
    return text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length;
  }

  /**
   * Split text into sentences
   * @param {string} text - Text to split
   * @returns {Array<string>} Array of sentences
   */
  splitIntoSentences(text) {
    if (!text) return [];
    
    // Simple sentence splitting - can be enhanced with more sophisticated NLP
    return text
      .split(/[.!?]+/)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0);
  }

  /**
   * Split text into paragraphs
   * @param {string} text - Text to split
   * @returns {Array<string>} Array of paragraphs
   */
  splitIntoParagraphs(text) {
    if (!text) return [];
    
    return text
      .split(/\n\s*\n/)
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0);
  }

  /**
   * Validate PDF file before processing
   * @param {File} file - File to validate
   * @returns {Object} Validation result
   */
  validateFile(file) {
    const errors = [];
    const maxSize = 50 * 1024 * 1024; // 50MB

    if (!file) {
      errors.push('No file provided');
    } else {
      if (file.type !== 'application/pdf') {
        errors.push('File must be a PDF document');
      }
      
      if (file.size > maxSize) {
        errors.push(`File size must be less than ${maxSize / (1024 * 1024)}MB`);
      }
      
      if (file.size === 0) {
        errors.push('File appears to be empty');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get text statistics
   * @param {string} text - Text to analyze
   * @returns {Object} Text statistics
   */
  getTextStatistics(text) {
    if (!text) {
      return {
        wordCount: 0,
        characterCount: 0,
        paragraphCount: 0,
        sentenceCount: 0,
        averageWordsPerSentence: 0,
        averageCharactersPerWord: 0,
      };
    }

    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const sentences = this.splitIntoSentences(text);
    const paragraphs = this.splitIntoParagraphs(text);

    return {
      wordCount: words.length,
      characterCount: text.length,
      paragraphCount: paragraphs.length,
      sentenceCount: sentences.length,
      averageWordsPerSentence: sentences.length > 0 ? Math.round(words.length / sentences.length * 100) / 100 : 0,
      averageCharactersPerWord: words.length > 0 ? Math.round(text.length / words.length * 100) / 100 : 0,
    };
  }
}

// Export singleton instance
export default new PDFService();
