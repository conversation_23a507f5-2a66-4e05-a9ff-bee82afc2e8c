import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle } from 'lucide-react';
import clsx from 'clsx';

const PDFUpload = ({ onFileUpload, isProcessing = false }) => {
  const [uploadError, setUploadError] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    setUploadError(null);
    
    if (rejectedFiles.length > 0) {
      const error = rejectedFiles[0].errors[0];
      setUploadError(error.message);
      return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setUploadedFile(file);
      onFileUpload(file);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: isProcessing
  });

  const removeFile = () => {
    setUploadedFile(null);
    setUploadError(null);
    onFileUpload(null);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full">
      {!uploadedFile ? (
        <div
          {...getRootProps()}
          className={clsx(
            'relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-500 group',
            'hover:scale-[1.02] hover:shadow-2xl',
            'focus:outline-none focus:ring-4 focus:ring-blue-500/20',
            {
              'border-blue-400 bg-gradient-to-br from-blue-50/80 to-purple-50/80 backdrop-blur-sm scale-[1.02] shadow-2xl': isDragActive,
              'border-gray-300 bg-white/60 backdrop-blur-sm hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50/80 hover:to-purple-50/80': !isDragActive && !uploadError,
              'border-red-300 bg-red-50/80 backdrop-blur-sm': uploadError,
              'opacity-50 cursor-not-allowed': isProcessing,
            }
          )}
        >
          <input {...getInputProps()} />

          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="relative flex flex-col items-center space-y-6">
            <div className="relative">
              <div className={clsx(
                'absolute inset-0 rounded-2xl blur transition-all duration-500',
                {
                  'bg-gradient-to-r from-blue-500/30 to-purple-500/30': isDragActive,
                  'bg-gradient-to-r from-gray-500/20 to-blue-500/20 group-hover:from-blue-500/30 group-hover:to-purple-500/30': !isDragActive && !uploadError,
                  'bg-red-500/30': uploadError,
                }
              )}></div>
              <div className={clsx(
                'relative p-6 rounded-2xl transition-all duration-500 transform group-hover:scale-110',
                {
                  'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg': isDragActive,
                  'bg-white/80 text-gray-600 group-hover:bg-gradient-to-r group-hover:from-blue-500 group-hover:to-purple-500 group-hover:text-white group-hover:shadow-lg': !isDragActive && !uploadError,
                  'bg-red-100 text-red-600': uploadError,
                }
              )}>
                {uploadError ? (
                  <AlertCircle className="w-10 h-10" />
                ) : (
                  <Upload className="w-10 h-10" />
                )}
              </div>
            </div>

            <div className="text-center space-y-2">
              <h3 className={clsx(
                'text-xl font-semibold transition-colors duration-300',
                {
                  'text-white': isDragActive,
                  'text-gray-800 group-hover:text-white': !isDragActive && !uploadError,
                  'text-red-700': uploadError,
                }
              )}>
                {uploadError ? 'Upload Failed' : isDragActive ? 'Drop your PDF here' : 'Upload PDF Document'}
              </h3>
              <p className={clsx(
                'text-sm transition-colors duration-300',
                {
                  'text-white/90': isDragActive,
                  'text-gray-600 group-hover:text-white/90': !isDragActive && !uploadError,
                  'text-red-600': uploadError,
                }
              )}>
                {uploadError ? uploadError : 'Drag and drop your PDF file here, or click to browse'}
              </p>
              {!uploadError && (
                <p className={clsx(
                  'text-xs transition-colors duration-300',
                  {
                    'text-white/70': isDragActive,
                    'text-gray-500 group-hover:text-white/70': !isDragActive,
                  }
                )}>
                  Maximum file size: 10MB
                </p>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-2xl blur"></div>
          <div className="relative bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500/20 rounded-xl blur"></div>
                  <div className="relative p-3 bg-green-100/80 rounded-xl">
                    <FileText className="w-8 h-8 text-green-600" />
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 mb-1 flex items-center space-x-2">
                    <span>{uploadedFile.name}</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse ml-2"></div>
                  </h3>
                  <p className="text-gray-600 flex items-center space-x-2">
                    <span>{formatFileSize(uploadedFile.size)}</span>
                    <span>•</span>
                    <span>PDF Document</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse ml-2"></div>
                  </p>
                </div>
              </div>

              {!isProcessing && (
                <button
                  onClick={removeFile}
                  className="group relative p-3 text-gray-400 hover:text-red-500 hover:bg-red-50/80 rounded-xl transition-all duration-300 transform hover:scale-110"
                  title="Remove file"
                >
                  <div className="absolute inset-0 bg-red-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <X className="relative w-5 h-5" />
                </button>
              )}
            </div>

            {isProcessing && (
              <div className="mt-6 pt-6 border-t border-white/30">
                <div className="flex items-center justify-center space-x-4">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-6 w-6 border-3 border-blue-500/30 border-t-blue-500"></div>
                    <div className="absolute inset-0 rounded-full bg-blue-500/10 animate-pulse"></div>
                  </div>
                  <span className="text-gray-700 font-medium">Processing PDF...</span>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {uploadError && (
        <div className="mt-6 relative group animate-slide-up">
          <div className="absolute inset-0 bg-red-500/10 rounded-2xl blur"></div>
          <div className="relative bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-6 shadow-lg">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-red-100 rounded-xl">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold text-red-800 mb-1">Upload Error</h3>
                <p className="text-red-700">{uploadError}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFUpload;
