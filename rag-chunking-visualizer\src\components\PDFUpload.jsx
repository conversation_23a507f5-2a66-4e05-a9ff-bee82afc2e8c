import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle } from 'lucide-react';
import clsx from 'clsx';

const PDFUpload = ({ onFileUpload, isProcessing = false }) => {
  const [uploadError, setUploadError] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    setUploadError(null);
    
    if (rejectedFiles.length > 0) {
      const error = rejectedFiles[0].errors[0];
      setUploadError(error.message);
      return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setUploadedFile(file);
      onFileUpload(file);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024, // 50MB
    disabled: isProcessing
  });

  const removeFile = () => {
    setUploadedFile(null);
    setUploadError(null);
    onFileUpload(null);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full">
      {!uploadedFile ? (
        <div
          {...getRootProps()}
          className={clsx(
            'border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300',
            'hover:border-primary-400 hover:bg-primary-50/50 hover:scale-[1.02]',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
            {
              'border-primary-400 bg-primary-50/50 scale-[1.02]': isDragActive,
              'border-secondary-300 bg-white': !isDragActive && !uploadError,
              'border-red-300 bg-red-50': uploadError,
              'opacity-50 cursor-not-allowed': isProcessing,
            }
          )}
        >
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center space-y-4">
            <div className={clsx(
              'p-4 rounded-full transition-colors duration-300',
              {
                'bg-primary-100 text-primary-600': isDragActive,
                'bg-secondary-100 text-secondary-600': !isDragActive && !uploadError,
                'bg-red-100 text-red-600': uploadError,
              }
            )}>
              {uploadError ? (
                <AlertCircle className="w-8 h-8" />
              ) : (
                <Upload className="w-8 h-8" />
              )}
            </div>
            
            <div>
              <p className="text-lg font-semibold text-secondary-900 mb-2">
                {isDragActive ? 'Drop your PDF here' : 'Upload PDF Document'}
              </p>
              <p className="text-sm text-secondary-600 mb-4">
                Drag and drop your PDF file here, or click to browse
              </p>
              <p className="text-xs text-secondary-500">
                Maximum file size: 50MB • Supported format: PDF
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white border border-secondary-200 rounded-xl p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-primary-100 rounded-lg">
                <FileText className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="font-semibold text-secondary-900 truncate max-w-xs">
                  {uploadedFile.name}
                </h3>
                <p className="text-sm text-secondary-600">
                  {formatFileSize(uploadedFile.size)} • PDF Document
                </p>
              </div>
            </div>
            
            {!isProcessing && (
              <button
                onClick={removeFile}
                className="p-2 text-secondary-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200"
                title="Remove file"
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
          
          {isProcessing && (
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary-600 border-t-transparent"></div>
                <span className="text-sm text-secondary-600">Processing PDF...</span>
              </div>
            </div>
          )}
        </div>
      )}
      
      {uploadError && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-sm text-red-700">{uploadError}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFUpload;
