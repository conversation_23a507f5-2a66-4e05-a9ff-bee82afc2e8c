// Test utility for validating chunking strategies
import chunkingStrategies from '../services/chunkingStrategies.js';
import { sampleText } from './sampleText.js';

/**
 * Test all chunking strategies with sample text
 * @returns {Object} Test results for all strategies
 */
export const testAllStrategies = () => {
  const results = {};
  
  // Test Fixed-Size Chunking
  try {
    const fixedSizeResult = chunkingStrategies.fixedSize.chunk(sampleText, {
      chunkSize: 500,
      overlap: 50,
      unit: 'characters'
    });
    results.fixedSize = {
      success: true,
      chunkCount: fixedSizeResult.length,
      averageSize: Math.round(fixedSizeResult.reduce((sum, chunk) => sum + chunk.content.length, 0) / fixedSizeResult.length),
      chunks: fixedSizeResult
    };
  } catch (error) {
    results.fixedSize = { success: false, error: error.message };
  }

  // Test Sentence-Based Chunking
  try {
    const sentenceResult = chunkingStrategies.sentenceBased.chunk(sampleText, {
      maxChunkSize: 800,
      sentencesPerChunk: 3,
      mode: 'sentences'
    });
    results.sentenceBased = {
      success: true,
      chunkCount: sentenceResult.length,
      averageSize: Math.round(sentenceResult.reduce((sum, chunk) => sum + chunk.content.length, 0) / sentenceResult.length),
      chunks: sentenceResult
    };
  } catch (error) {
    results.sentenceBased = { success: false, error: error.message };
  }

  // Test Paragraph-Based Chunking
  try {
    const paragraphResult = chunkingStrategies.paragraphBased.chunk(sampleText, {
      maxChunkSize: 1000,
      minChunkSize: 200,
      combineSmallParagraphs: true
    });
    results.paragraphBased = {
      success: true,
      chunkCount: paragraphResult.length,
      averageSize: Math.round(paragraphResult.reduce((sum, chunk) => sum + chunk.content.length, 0) / paragraphResult.length),
      chunks: paragraphResult
    };
  } catch (error) {
    results.paragraphBased = { success: false, error: error.message };
  }

  // Test Recursive Chunking
  try {
    const recursiveResult = chunkingStrategies.recursive.chunk(sampleText, {
      chunkSize: 600,
      overlap: 100,
      separators: ['\n\n', '\n', '. ', ' ']
    });
    results.recursive = {
      success: true,
      chunkCount: recursiveResult.length,
      averageSize: Math.round(recursiveResult.reduce((sum, chunk) => sum + chunk.content.length, 0) / recursiveResult.length),
      chunks: recursiveResult
    };
  } catch (error) {
    results.recursive = { success: false, error: error.message };
  }

  return results;
};

/**
 * Run a quick validation test
 * @returns {boolean} True if all basic tests pass
 */
export const runQuickTest = () => {
  console.log('🧪 Running RAG Chunking Visualizer Tests...');
  
  const results = testAllStrategies();
  let allPassed = true;
  
  Object.entries(results).forEach(([strategy, result]) => {
    if (result.success) {
      console.log(`✅ ${strategy}: ${result.chunkCount} chunks, avg size: ${result.averageSize} chars`);
    } else {
      console.error(`❌ ${strategy}: ${result.error}`);
      allPassed = false;
    }
  });
  
  if (allPassed) {
    console.log('🎉 All chunking strategies working correctly!');
  } else {
    console.log('⚠️ Some chunking strategies have issues.');
  }
  
  return allPassed;
};

// Auto-run test in development
if (import.meta.env.DEV) {
  // Delay test to ensure all modules are loaded
  setTimeout(() => {
    runQuickTest();
  }, 1000);
}
