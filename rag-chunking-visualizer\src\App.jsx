import React, { useState, useCallback } from 'react';
import { FileText, Layers, AlertCircle, Play } from 'lucide-react';
import PDFUpload from './components/PDFUpload';
import StrategySelector from './components/StrategySelector';
import ChunkVisualization from './components/ChunkVisualization';
import pdfService from './services/pdfService';
import chunkingStrategies from './services/chunkingStrategies';
import { sampleText, sampleMetadata } from './utils/sampleText';
import './utils/testChunking'; // Auto-run tests in development

function App() {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [textMetadata, setTextMetadata] = useState(null);
  const [selectedStrategy, setSelectedStrategy] = useState('');
  const [strategyConfig, setStrategyConfig] = useState({});
  const [chunks, setChunks] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  const handleDemoMode = useCallback(() => {
    setUploadedFile(null);
    setExtractedText(sampleText);
    setTextMetadata(sampleMetadata);
    setChunks([]);
    setError(null);
  }, []);

  const handleFileUpload = useCallback(async (file) => {
    if (!file) {
      setUploadedFile(null);
      setExtractedText('');
      setTextMetadata(null);
      setChunks([]);
      setError(null);
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await pdfService.extractText(file);

      if (result.success) {
        setUploadedFile(file);
        setExtractedText(result.data.text);
        setTextMetadata(result.data.metadata);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to process PDF file. Please try again.');
      console.error('PDF processing error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const handleStrategyChange = useCallback((strategy) => {
    setSelectedStrategy(strategy);
    setChunks([]);
    setError(null);
  }, []);

  const handleConfigChange = useCallback((config) => {
    setStrategyConfig(config);
  }, []);

  const handleExecuteChunking = useCallback(async () => {
    if (!extractedText || !selectedStrategy) return;

    setIsProcessing(true);
    setError(null);

    try {
      const strategy = chunkingStrategies[selectedStrategy];
      if (!strategy) {
        throw new Error('Invalid chunking strategy selected');
      }

      const result = strategy.chunk(extractedText, strategyConfig);
      setChunks(result);
    } catch (err) {
      setError('Failed to apply chunking strategy. Please try again.');
      console.error('Chunking error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, [extractedText, selectedStrategy, strategyConfig]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary-50 to-primary-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-secondary-200 animate-slide-up">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-primary-100 to-blue-100 rounded-lg shadow-sm">
              <Layers className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                RAG Chunking Visualizer
              </h1>
              <p className="text-sm text-secondary-600">
                Analyze and compare different text chunking strategies for RAG systems
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Welcome Section */}
        {!extractedText && !isProcessing && (
          <div className="text-center mb-8 animate-fade-in">
            <div className="max-w-2xl mx-auto">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Get Started with RAG Chunking
              </h2>
              <p className="text-lg text-secondary-600 mb-8">
                Upload a PDF document or try our demo to explore different text chunking strategies for your RAG system.
              </p>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Upload Section */}
          <div className="bg-white rounded-2xl shadow-lg border border-secondary-100 overflow-hidden animate-scale-in hover:shadow-xl transition-shadow duration-300">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-secondary-900 flex items-center">
                  <FileText className="w-6 h-6 mr-3 text-primary-600" />
                  Document Upload
                </h2>
                {!extractedText && (
                  <button
                    onClick={handleDemoMode}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                    disabled={isProcessing}
                  >
                    <Play className="w-4 h-4" />
                    <span>Try Demo</span>
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Upload Area */}
                <div className="lg:col-span-2">
                  <PDFUpload
                    onFileUpload={handleFileUpload}
                    isProcessing={isProcessing}
                  />
                </div>

                {/* Document Info */}
                {textMetadata && (
                  <div className="bg-gradient-to-br from-secondary-50 to-primary-50 rounded-xl p-4">
                    <h3 className="font-semibold text-secondary-900 mb-3 text-sm uppercase tracking-wide">Document Stats</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-secondary-600 text-sm">Pages</span>
                        <span className="font-bold text-primary-700">{textMetadata.pages}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-secondary-600 text-sm">Words</span>
                        <span className="font-bold text-primary-700">{textMetadata.wordCount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-secondary-600 text-sm">Characters</span>
                        <span className="font-bold text-primary-700">{textMetadata.characterCount.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-secondary-600 text-sm">Paragraphs</span>
                        <span className="font-bold text-primary-700">{textMetadata.paragraphCount}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Strategy Selection */}
          {extractedText && (
            <div className="bg-white rounded-2xl shadow-lg border border-secondary-100 overflow-hidden animate-scale-in hover:shadow-xl transition-shadow duration-300">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center">
                  <Layers className="w-6 h-6 mr-3 text-primary-600" />
                  Chunking Strategy
                </h2>
                <StrategySelector
                  selectedStrategy={selectedStrategy}
                  onStrategyChange={handleStrategyChange}
                  onConfigChange={handleConfigChange}
                  onExecute={handleExecuteChunking}
                  isProcessing={isProcessing}
                />
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 rounded-xl p-4 animate-slide-up">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <p className="text-red-700 font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Results Section */}
          {(chunks.length > 0 || (extractedText && selectedStrategy)) && (
            <div className="bg-white rounded-2xl shadow-lg border border-secondary-100 overflow-hidden animate-scale-in hover:shadow-xl transition-shadow duration-300">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center">
                  <Layers className="w-6 h-6 mr-3 text-primary-600" />
                  Chunking Results
                </h2>
                <ChunkVisualization
                  chunks={chunks}
                  originalText={extractedText}
                  strategy={selectedStrategy}
                />
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
