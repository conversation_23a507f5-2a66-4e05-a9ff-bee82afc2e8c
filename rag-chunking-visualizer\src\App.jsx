import React, { useState, useCallback } from 'react';
import { FileText, Layers, AlertCircle, Play } from 'lucide-react';
import PDFUpload from './components/PDFUpload';
import StrategySelector from './components/StrategySelector';
import ChunkVisualization from './components/ChunkVisualization';
import pdfService from './services/pdfService';
import chunkingStrategies from './services/chunkingStrategies';
import { sampleText, sampleMetadata } from './utils/sampleText';
import './utils/testChunking'; // Auto-run tests in development

function App() {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [textMetadata, setTextMetadata] = useState(null);
  const [selectedStrategy, setSelectedStrategy] = useState('');
  const [strategyConfig, setStrategyConfig] = useState({});
  const [chunks, setChunks] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  const handleDemoMode = useCallback(() => {
    setUploadedFile(null);
    setExtractedText(sampleText);
    setTextMetadata(sampleMetadata);
    setChunks([]);
    setError(null);
  }, []);

  const handleFileUpload = useCallback(async (file) => {
    if (!file) {
      setUploadedFile(null);
      setExtractedText('');
      setTextMetadata(null);
      setChunks([]);
      setError(null);
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await pdfService.extractText(file);

      if (result.success) {
        setUploadedFile(file);
        setExtractedText(result.data.text);
        setTextMetadata(result.data.metadata);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to process PDF file. Please try again.');
      console.error('PDF processing error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const handleStrategyChange = useCallback((strategy) => {
    setSelectedStrategy(strategy);
    setChunks([]);
    setError(null);
  }, []);

  const handleConfigChange = useCallback((config) => {
    setStrategyConfig(config);
  }, []);

  const handleExecuteChunking = useCallback(async () => {
    if (!extractedText || !selectedStrategy) return;

    setIsProcessing(true);
    setError(null);

    try {
      const strategy = chunkingStrategies[selectedStrategy];
      if (!strategy) {
        throw new Error('Invalid chunking strategy selected');
      }

      const result = strategy.chunk(extractedText, strategyConfig);
      setChunks(result);
    } catch (err) {
      setError('Failed to apply chunking strategy. Please try again.');
      console.error('Chunking error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, [extractedText, selectedStrategy, strategyConfig]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg shadow-lg border-b border-white/20 animate-fade-in sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-75"></div>
                <div className="relative p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl">
                  <Layers className="h-8 w-8 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  RAG Chunking Visualizer
                </h1>
                <p className="text-gray-600 mt-2 text-lg">Analyze and compare different text chunking strategies for RAG systems</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-purple-50 px-4 py-2 rounded-full border border-blue-200">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">Live Preview</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        {/* Welcome Section */}
        {!extractedText && !isProcessing && (
          <div className="text-center mb-16 animate-fade-in">
            <div className="max-w-4xl mx-auto">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-3xl blur-3xl"></div>
                <div className="relative bg-white/60 backdrop-blur-sm rounded-3xl p-12 border border-white/30 shadow-2xl">
                  <h2 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
                    Get Started with RAG Chunking
                  </h2>
                  <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                    Upload a PDF document or try our demo to explore different text chunking strategies for your RAG system.
                  </p>
                  <div className="flex items-center justify-center space-x-8 text-sm text-gray-500">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Multiple Strategies</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span>Real-time Visualization</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Interactive Analysis</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-8">
          {/* Upload Section */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
            <div className="relative bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden animate-scale-in hover:shadow-3xl transition-all duration-500">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur opacity-75"></div>
                      <div className="relative p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl">
                        <FileText className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Document Upload</h2>
                      <p className="text-gray-600">Upload your PDF or try our demo</p>
                    </div>
                  </div>
                  {!extractedText && (
                    <button
                      onClick={handleDemoMode}
                      className="group relative overflow-hidden px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                      disabled={isProcessing}
                    >
                      <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                      <div className="relative flex items-center space-x-2">
                        <Play className="w-5 h-5" />
                        <span className="font-semibold">Try Demo</span>
                      </div>
                    </button>
                  )}
                </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Upload Area */}
                <div className="lg:col-span-2">
                  <PDFUpload
                    onFileUpload={handleFileUpload}
                    isProcessing={isProcessing}
                  />
                </div>

                {/* Document Info */}
                {textMetadata && (
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-2xl blur"></div>
                    <div className="relative bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/30 shadow-lg">
                      <h3 className="font-bold text-gray-900 mb-6 text-lg flex items-center">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mr-3"></div>
                        Document Stats
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/20">
                          <span className="text-gray-600 font-medium">Pages</span>
                          <span className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{textMetadata.pages}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/20">
                          <span className="text-gray-600 font-medium">Words</span>
                          <span className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{textMetadata.wordCount.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/20">
                          <span className="text-gray-600 font-medium">Characters</span>
                          <span className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{textMetadata.characterCount.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/20">
                          <span className="text-gray-600 font-medium">Paragraphs</span>
                          <span className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{textMetadata.paragraphCount}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Strategy Selection */}
          {extractedText && (
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-blue-600/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden animate-scale-in hover:shadow-3xl transition-all duration-500">
                <div className="p-8">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl blur opacity-75"></div>
                      <div className="relative p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
                        <Layers className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Chunking Strategy</h2>
                      <p className="text-gray-600">Choose and configure your chunking approach</p>
                    </div>
                  </div>
                  <StrategySelector
                    selectedStrategy={selectedStrategy}
                    onStrategyChange={handleStrategyChange}
                    onConfigChange={handleConfigChange}
                    onExecute={handleExecuteChunking}
                    isProcessing={isProcessing}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="relative group animate-slide-up">
              <div className="absolute inset-0 bg-red-500/10 rounded-2xl blur"></div>
              <div className="relative bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-2xl p-6 shadow-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-red-100 rounded-xl">
                    <AlertCircle className="w-6 h-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-red-800 mb-1">Error</h3>
                    <p className="text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Results Section */}
          {(chunks.length > 0 || (extractedText && selectedStrategy)) && (
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-green-600/10 to-blue-600/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/30 overflow-hidden animate-scale-in hover:shadow-3xl transition-all duration-500">
                <div className="p-8">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl blur opacity-75"></div>
                      <div className="relative p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl">
                        <Layers className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Chunking Results</h2>
                      <p className="text-gray-600">Visualize and analyze your text chunks</p>
                    </div>
                  </div>
                  <ChunkVisualization
                    chunks={chunks}
                    originalText={extractedText}
                    strategy={selectedStrategy}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default App;
