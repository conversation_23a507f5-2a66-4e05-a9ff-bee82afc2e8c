import React, { useState, useCallback } from 'react';
import { FileText, Layers, AlertCircle, Play } from 'lucide-react';
import PDFUpload from './components/PDFUpload';
import StrategySelector from './components/StrategySelector';
import ChunkVisualization from './components/ChunkVisualization';
import pdfService from './services/pdfService';
import chunkingStrategies from './services/chunkingStrategies';
import { sampleText, sampleMetadata } from './utils/sampleText';
import './utils/testChunking'; // Auto-run tests in development

function App() {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [textMetadata, setTextMetadata] = useState(null);
  const [selectedStrategy, setSelectedStrategy] = useState('');
  const [strategyConfig, setStrategyConfig] = useState({});
  const [chunks, setChunks] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  const handleDemoMode = useCallback(() => {
    setUploadedFile(null);
    setExtractedText(sampleText);
    setTextMetadata(sampleMetadata);
    setChunks([]);
    setError(null);
  }, []);

  const handleFileUpload = useCallback(async (file) => {
    if (!file) {
      setUploadedFile(null);
      setExtractedText('');
      setTextMetadata(null);
      setChunks([]);
      setError(null);
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const result = await pdfService.extractText(file);

      if (result.success) {
        setUploadedFile(file);
        setExtractedText(result.data.text);
        setTextMetadata(result.data.metadata);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to process PDF file. Please try again.');
      console.error('PDF processing error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const handleStrategyChange = useCallback((strategy) => {
    setSelectedStrategy(strategy);
    setChunks([]);
    setError(null);
  }, []);

  const handleConfigChange = useCallback((config) => {
    setStrategyConfig(config);
  }, []);

  const handleExecuteChunking = useCallback(async () => {
    if (!extractedText || !selectedStrategy) return;

    setIsProcessing(true);
    setError(null);

    try {
      const strategy = chunkingStrategies[selectedStrategy];
      if (!strategy) {
        throw new Error('Invalid chunking strategy selected');
      }

      const result = strategy.chunk(extractedText, strategyConfig);
      setChunks(result);
    } catch (err) {
      setError('Failed to apply chunking strategy. Please try again.');
      console.error('Chunking error:', err);
    } finally {
      setIsProcessing(false);
    }
  }, [extractedText, selectedStrategy, strategyConfig]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary-50 to-primary-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-100 rounded-lg">
              <Layers className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                RAG Chunking Visualizer
              </h1>
              <p className="text-sm text-secondary-600">
                Analyze and compare different text chunking strategies for RAG systems
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Upload and Strategy */}
          <div className="lg:col-span-1 space-y-6">
            {/* PDF Upload */}
            <div>
              <h2 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2 text-primary-600" />
                Document Upload
              </h2>
              <PDFUpload
                onFileUpload={handleFileUpload}
                isProcessing={isProcessing}
              />

              {/* Demo Mode Button */}
              <div className="mt-4">
                <button
                  onClick={handleDemoMode}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-secondary-100 hover:bg-secondary-200 text-secondary-700 rounded-lg transition-colors duration-200"
                  disabled={isProcessing}
                >
                  <Play className="w-4 h-4" />
                  <span>Try Demo with Sample Text</span>
                </button>
              </div>
            </div>

            {/* Document Info */}
            {textMetadata && (
              <div className="bg-white rounded-xl border border-secondary-200 p-4">
                <h3 className="font-semibold text-secondary-900 mb-3">Document Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Pages:</span>
                    <span className="font-medium">{textMetadata.pages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Words:</span>
                    <span className="font-medium">{textMetadata.wordCount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Characters:</span>
                    <span className="font-medium">{textMetadata.characterCount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-secondary-600">Paragraphs:</span>
                    <span className="font-medium">{textMetadata.paragraphCount}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Strategy Selection */}
            {extractedText && (
              <StrategySelector
                selectedStrategy={selectedStrategy}
                onStrategyChange={handleStrategyChange}
                onConfigChange={handleConfigChange}
                onExecute={handleExecuteChunking}
                isProcessing={isProcessing}
              />
            )}
          </div>

          {/* Right Column - Results */}
          <div className="lg:col-span-2">
            <h2 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center">
              <Layers className="w-5 h-5 mr-2 text-primary-600" />
              Chunking Results
            </h2>

            {/* Error Display */}
            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <p className="text-red-700">{error}</p>
                </div>
              </div>
            )}

            {/* Chunk Visualization */}
            <ChunkVisualization
              chunks={chunks}
              originalText={extractedText}
              strategy={selectedStrategy}
            />
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
